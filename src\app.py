# from flask import Flask, request, jsonify
# from flask_cors import CORS
# import sqlite3
# import requests
# from google.oauth2 import service_account
# from google.auth.transport.requests import Request

# app = Flask(__name__)
# CORS(app, resources={r"/api/*": {"origins": "*"}})  # Allow requests from all origins

# # Path to your service account JSON file
# SERVICE_ACCOUNT_FILE = "config/sasthra-3a69e-firebase-adminsdk-fbsvc-d72ca742ef.json"

# # Firebase HTTP v1 API endpoint
# FCM_ENDPOINT = "https://fcm.googleapis.com/v1/projects/sasthra-3a69e/messages:send"

# # Initialize SQLite database to store tokens
# def init_db():
#     conn = sqlite3.connect("tokens.db")
#     c = conn.cursor()
#     c.execute("""CREATE TABLE IF NOT EXISTS tokens (token TEXT PRIMARY KEY)""")
#     conn.commit()
#     conn.close()

# init_db()

# # Get OAuth 2.0 access token
# def get_access_token():
#     credentials = service_account.Credentials.from_service_account_file(
#         SERVICE_ACCOUNT_FILE, scopes=["https://www.googleapis.com/auth/firebase.messaging"]
#     )
#     credentials.refresh(Request())
#     return credentials.token

# # Endpoint to save FCM token
# @app.route("/api/save-token", methods=["POST"])
# def save_token():
#     data = request.get_json()
#     token = data.get("token")
#     if not token:
#         return jsonify({"error": "Token is required"}), 400

#     conn = sqlite3.connect("tokens.db")
#     c = conn.cursor()
#     c.execute("INSERT OR REPLACE INTO tokens (token) VALUES (?)", (token,))
#     conn.commit()
#     conn.close()
#     return jsonify({"message": "Token saved"}), 200

# # Endpoint to send notification
# @app.route("/api/send-notification", methods=["POST"])
# def send_notification():
#     data = request.get_json()
#     title = data.get("title", "Default Title")
#     body = data.get("body", "Default Body")

#     # Fetch all tokens
#     conn = sqlite3.connect("tokens.db")
#     c = conn.cursor()
#     c.execute("SELECT token FROM tokens")
#     tokens = [row[0] for row in c.fetchall()]
#     conn.close()

#     if not tokens:
#         return jsonify({"error": "No tokens found"}), 400

#     # Get OAuth 2.0 access token
#     access_token = get_access_token()

#     # Send notification to each token
#     results = []
#     for token in tokens:
#         payload = {
#             "message": {
#                 "token": token,
#                 "notification": {
#                     "title": title,
#                     "body": body
#                 }
#             }
#         }
#         headers = {
#             "Authorization": f"Bearer {access_token}",
#             "Content-Type": "application/json"
#         }
#         response = requests.post(FCM_ENDPOINT, json=payload, headers=headers)
#         results.append({"token": token, "status": response.status_code, "response": response.json()})

#     return jsonify({"message": "Notifications sent", "results": results}), 200

# if __name__ == "__main__":
#     app.run(host="0.0.0.0", port=8045, debug=True)




from flask import Flask, request, jsonify
from flask_cors import CORS
import requests
from google.oauth2 import service_account
from google.auth.transport.requests import Request
from dotenv import load_dotenv
import os
import atexit
from config.database import get_db_connection, return_db_connection, init_postgres_db, cleanup_connection_pool

load_dotenv()

app = Flask(__name__)
CORS(app, resources={r"/api/*": {"origins": "*"}})

SERVICE_ACCOUNT_FILE = "src/config/sasthra-3a69e-firebase-adminsdk-fbsvc-d72ca742ef.json"
FCM_ENDPOINT = "https://fcm.googleapis.com/v1/projects/sasthra-3a69e/messages:send"

# Initialize database and connection pool
init_postgres_db()

# Cleanup on app shutdown
atexit.register(cleanup_connection_pool)

def get_access_token():
    credentials = service_account.Credentials.from_service_account_file(
        SERVICE_ACCOUNT_FILE, scopes=["https://www.googleapis.com/auth/firebase.messaging"]
    )
    credentials.refresh(Request())
    return credentials.token

@app.route("/api/save-token", methods=["POST"])
def save_token():
    data = request.get_json()
    token = data.get("token")
    user_id = data.get("userId")
    
    # Handle undefined values as null
    name = data.get("name") if data.get("name") != "undefined" else None
    phone = data.get("phone") if data.get("phone") != "undefined" else None
    role = data.get("role") if data.get("role") != "undefined" else None
    designation = data.get("designation") if data.get("designation") != "undefined" else None
    center_name = data.get("centername") if data.get("centername") != "undefined" else None
    center_code = data.get("centercode") if data.get("centercode") != "undefined" else None
    course = data.get("course") if data.get("course") != "undefined" else None
    batch_id = data.get("batch") if data.get("batch") != "undefined" else None
    batch_name = data.get("batchname") if data.get("batchname") != "undefined" else None
    
    if not token or not user_id:
        return jsonify({"error": "Token and userId are required"}), 400

    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO user_notifications 
            (user_id, token, name, phone, role, designation, center_name, center_code, course, batch_id, batch_name, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
            ON CONFLICT (user_id) 
            DO UPDATE SET 
                token = EXCLUDED.token,
                name = EXCLUDED.name,
                phone = EXCLUDED.phone,
                role = EXCLUDED.role,
                designation = EXCLUDED.designation,
                center_name = EXCLUDED.center_name,
                center_code = EXCLUDED.center_code,
                course = EXCLUDED.course,
                batch_id = EXCLUDED.batch_id,
                batch_name = EXCLUDED.batch_name,
                updated_at = CURRENT_TIMESTAMP
        """, (user_id, token, name, phone, role, designation, center_name, center_code, course, batch_id, batch_name))
        
        conn.commit()
        cursor.close()
        
        return jsonify({"message": "User subscribed to notifications successfully"}), 200
        
    except Exception as e:
        if conn:
            conn.rollback()
        return jsonify({"error": f"Database error: {str(e)}"}), 500
    finally:
        if conn:
            return_db_connection(conn)

@app.route("/api/send-personalized-notification", methods=["POST"])
def send_personalized_notification():
    data = request.get_json()
    user_ids = data.get("user_ids", [])
    title = data.get("title", "Default Title")
    body = data.get("body", "Default Body")
    
    if not user_ids:
        return jsonify({"error": "user_ids are required"}), 400
    
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        placeholders = ','.join(['%s'] * len(user_ids))
        cursor.execute(f"SELECT user_id, token, name, designation, role FROM user_notifications WHERE user_id IN ({placeholders})", user_ids)
        users = cursor.fetchall()
        
        cursor.close()
        
        if not users:
            return jsonify({"error": "No users found"}), 400
        
        access_token = get_access_token()
        results = []
        
        for user in users:
            personalized_body = f"Hi {user['name'] or 'User'}, {body}"
            payload = {
                "message": {
                    "token": user['token'],
                    "notification": {
                        "title": title,
                        "body": personalized_body
                    }
                }
            }
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            response = requests.post(FCM_ENDPOINT, json=payload, headers=headers)
            results.append({
                "user_id": user['user_id'],
                "name": user['name'],
                "status": response.status_code
            })
        
        return jsonify({"message": "Personalized notifications sent", "results": results}), 200
        
    except Exception as e:
        return jsonify({"error": f"Error: {str(e)}"}), 500
    finally:
        if conn:
            return_db_connection(conn)

@app.route("/api/send-notification", methods=["POST"])
def send_notification():
    data = request.get_json()
    title = data.get("title", "Default Title")
    body = data.get("body", "Default Body")

    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT token FROM user_notifications")
        tokens = [row['token'] for row in cursor.fetchall()]
        cursor.close()

        if not tokens:
            return jsonify({"error": "No tokens found"}), 400

        access_token = get_access_token()
        results = []
        
        for token in tokens:
            payload = {
                "message": {
                    "token": token,
                    "notification": {
                        "title": title,
                        "body": body
                    }
                }
            }
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            response = requests.post(FCM_ENDPOINT, json=payload, headers=headers)
            results.append({"token": token, "status": response.status_code})

        return jsonify({"message": "Notifications sent", "results": results}), 200
        
    except Exception as e:
        return jsonify({"error": f"Error: {str(e)}"}), 500
    finally:
        if conn:
            return_db_connection(conn)

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8045, debug=True)
