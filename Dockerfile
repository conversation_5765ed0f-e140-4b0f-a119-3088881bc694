# syntax=docker/dockerfile:1

FROM python:3.11-slim

WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create config directory and copy service account file
RUN mkdir -p config
COPY src/config/sasthra-3a69e-firebase-adminsdk-fbsvc-d72ca742ef.json ./config/

# Expose the port
EXPOSE 8045

# Run the application
CMD ["python", "src/app.py"]