import psycopg2
import os
from psycopg2.extras import RealDictCursor
from psycopg2.pool import ThreadedConnectionPool
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Connection pool
connection_pool = None

def init_connection_pool():
    global connection_pool
    try:
        connection_pool = ThreadedConnectionPool(
            minconn=1,
            maxconn=20,
            host=os.getenv('DB_HOST'),
            port=os.getenv('DB_PORT'),
            database=os.getenv('DB_NAME'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD'),
            cursor_factory=RealDictCursor
        )
        logger.info("Database connection pool initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize connection pool: {e}")
        raise

def get_db_connection():
    global connection_pool
    if connection_pool is None:
        init_connection_pool()
    
    try:
        conn = connection_pool.getconn()
        if conn.closed:
            connection_pool.putconn(conn, close=True)
            conn = connection_pool.getconn()
        return conn
    except Exception as e:
        logger.error(f"Failed to get database connection: {e}")
        # Fallback to direct connection
        return psycopg2.connect(
            host=os.getenv('DB_HOST'),
            port=os.getenv('DB_PORT'),
            database=os.getenv('DB_NAME'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD'),
            cursor_factory=RealDictCursor
        )

def return_db_connection(conn):
    global connection_pool
    if connection_pool and conn:
        try:
            connection_pool.putconn(conn)
        except Exception as e:
            logger.error(f"Failed to return connection to pool: {e}")
            try:
                conn.close()
            except:
                pass

def close_db_connection(conn):
    if conn:
        try:
            conn.close()
        except Exception as e:
            logger.error(f"Failed to close connection: {e}")

def init_postgres_db():
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_notifications (
                id SERIAL PRIMARY KEY,
                user_id VARCHAR(255) UNIQUE NOT NULL,
                token TEXT NOT NULL,
                name VARCHAR(255),
                phone VARCHAR(20),
                role VARCHAR(100),
                designation VARCHAR(255),
                center_name VARCHAR(255),
                center_code VARCHAR(100),
                course VARCHAR(255),
                batch_id VARCHAR(100),
                batch_name VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        conn.commit()
        cursor.close()
        logger.info("Database table initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise
    finally:
        if conn:
            if connection_pool:
                return_db_connection(conn)
            else:
                close_db_connection(conn)

def cleanup_connection_pool():
    global connection_pool
    if connection_pool:
        try:
            connection_pool.closeall()
            logger.info("Connection pool closed successfully")
        except Exception as e:
            logger.error(f"Failed to close connection pool: {e}")
